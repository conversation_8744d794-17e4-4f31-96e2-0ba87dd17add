<?php

namespace App\Models;

use App\Traits\HasSettings;
use App\Traits\HasYubicoOTP;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Passport\Contracts\OAuthenticatable;
use Laravel\Passport\HasApiTokens;
use Spatie\LaravelPasskeys\Models\Concerns\HasPasskeys;
use Spatie\LaravelPasskeys\Models\Concerns\InteractsWithPasskeys;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements HasPasskeys, MustVerifyEmail, OAuthenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, HasRoles, HasSettings, HasYubicoOTP, InteractsWithPasskeys, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'current_workspace_id',
        'current_balance',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'current_balance' => 'decimal:8',
        ];
    }

    protected $with = ['currentWorkspace'];

    /**
     * 获取用户的工作空间
     */
    public function workspaces()
    {
        return $this->hasMany(Workspace::class);
    }

    /**
     * 获取用户的充值记录
     */
    public function topUpRecords(): HasMany
    {
        return $this->hasMany(TopUpRecord::class);
    }

    /**
     * 获取已完成的充值记录
     */
    public function completedTopUpRecords(): HasMany
    {
        return $this->topUpRecords()->where('status', TopUpRecord::STATUS_COMPLETED);
    }

    /**
     * 获取有余额的充值记录
     */
    public function availableTopUpRecords(): HasMany
    {
        return $this->completedTopUpRecords()
            ->where('remaining_amount', '>', 0)
            ->orderBy('completed_at', 'asc'); // 先进先出
    }

    // 按照用户的余额排序
    public function scopeOrderByBalance($query)
    {
        return $query->orderBy('current_balance', 'desc');
    }

    /**
     * 获取用户的兑换码使用记录
     */
    public function redeemCodeUsages(): HasMany
    {
        return $this->hasMany(RedeemCodeUsage::class);
    }

    /**
     * 获取用户的 OAuth 客户端
     */
    public function clients(): HasMany
    {
        return $this->hasMany(OAuthClient::class, 'owner_id')->where('owner_type', User::class);
    }

    /**
     * 获取用户的 Webhook 端点
     */
    public function webhookEndpoints(): HasMany
    {
        return $this->hasMany(WebhookEndpoint::class);
    }

    /**
     * 设置当前工作空间
     */
    public function setCurrentWorkspace(Workspace $workspace)
    {
        if ($workspace->user_id !== $this->id) {
            throw new \InvalidArgumentException('You can only set your own workspace as current');
        }

        $this->update(['current_workspace_id' => $workspace->id]);

        // 设置权限检查的工作空间 ID
        setPermissionsTeamId($workspace->id);
    }

    /**
     * 检查用户是否有工作空间权限
     */
    public function hasWorkspacePermission(int $workspaceId, string $permission): bool
    {
        $workspace = Workspace::find($workspaceId);

        if (! $workspace || $workspace->user_id !== $this->id) {
            return false;
        }

        // 工作空间所有者拥有所有权限
        return true;
    }

    public function currentWorkspace(): BelongsTo
    {
        return $this->belongsTo(Workspace::class, 'current_workspace_id');
    }

    /**
     * 获取当前工作空间（方法形式）
     */
    public function getWorkspace(): ?Workspace
    {
        $workspaceId = getPermissionsTeamId() ?? $this->current_workspace_id;
        if (! $workspaceId) {
            return null;
        }

        return Workspace::with('cluster')->find($workspaceId);
    }

    /**
     * 检查余额是否充足
     */
    public function hasEnoughBalance(string $amount): bool
    {
        // 使用 BCMath 进行精确比较
        $currentBalance = number_format($this->current_balance, 8, '.', '');

        return bccomp($currentBalance, $amount, 8) >= 0;
    }

    /**
     * 判断用户是否在指定 ID 的工作空间中
     *
     * @param  int|Workspace|null  $team  工作空间模型或者 ID
     * @return bool 用户是否在该工作空间中
     */
    public function inWorkspace(int|Workspace|null $workspace): bool
    {
        // 如果工作空间 ID 为 null，直接返回 false
        if ($workspace === null) {
            return false;
        }

        $workspaceId = is_int($workspace) ? $workspace : $workspace->id;

        // 先检查当前工作空间，如果当前工作空间 ID 匹配，可以快速返回
        if ($this->currentWorkspace && $this->currentWorkspace->id === $workspaceId) {
            return true;
        }

        // 再检查拥有的工作空间，这通常是小集合，性能影响小
        return $this->workspaces->contains('id', $workspaceId);
    }

    /**
     * 获取当前工作空间（属性访问器）
     */
    // public function getCurrentWorkspaceAttribute(): ?Workspace
    // {
    //     return $this->getWorkspace();
    // }

    public function getClaims($scopes = []): array
    {
        $data = [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'avatar' => $this->avatar(),
        ];

        if (in_array('profile', $scopes)) {
            $data['name'] = $this->name;
            $data['preferred_username'] = $this->name;
            $data['email'] = $this->email;
            $data['email_verified'] = $this->email_verified_at !== null;
            $data['real_name_verified'] = $this->real_name_verified_at !== null;
            $data['phone_verified'] = $this->phone_verified_at !== null;
        }

        if (in_array('realname', $scopes)) {
            $data['real_name'] = $this->real_name;
            $data['id_card'] = $this->id_card;
        }

        if (in_array('phone', $scopes)) {
            $data['phone'] = $this->phone;
        }

        // 固定信息
        // roles
        $roles = $this->roles()->get()->pluck('name')->toArray();
        $permissions = $this->getAllPermissions()->pluck('name')->toArray();

        $data['roles'] = $roles;
        $data['permissions'] = $permissions;

        // 工作区
        $currentWorkspace = $this->getWorkspace();
        if ($currentWorkspace) {
            $data['workspace'] = $currentWorkspace;
        }

        $data['created_at'] = $this->created_at;

        return $data;
    }

    public function avatar(): string
    {
        return "https://cravatar.cn/avatar/{$this->email_md5}";
    }
}
