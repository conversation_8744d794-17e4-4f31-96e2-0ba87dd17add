<?php

namespace App\Events\K8s;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

abstract class BaseK8sResourceEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $namespace;

    public string $clusterName;

    public int $clusterId;

    public string $resourceType;

    public string $resourceName;

    public array $resource;

    public string $action; // created, updated, deleted

    public function __construct(
        string $namespace,
        string $clusterName,
        int $clusterId,
        string $resourceType,
        string $resourceName,
        array $resource,
        string $action
    ) {
        $this->namespace = $namespace;
        $this->clusterName = $clusterName;
        $this->clusterId = $clusterId;
        $this->resourceType = $resourceType;
        $this->resourceName = $resourceName;
        $this->resource = $resource;
        $this->action = $action;

        Log::debug('event fire');
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("workspace.{$this->namespace}.{$this->resourceType}"),
            new PrivateChannel("workspace.{$this->namespace}.resources"),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'namespace' => $this->namespace,
            'cluster' => [
                'id' => $this->clusterId,
                'name' => $this->clusterName,
            ],
            'resource_type' => $this->resourceType,
            'resource_name' => $this->resourceName,
            'action' => $this->action,
            'resource' => $this->resource,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Get the broadcast event name.
     */
    public function broadcastAs(): string
    {
        return "{$this->resourceType}.{$this->action}";
    }
}
