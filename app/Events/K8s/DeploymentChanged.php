<?php

namespace App\Events\K8s;

/**
 * Generic event for any deployment changes.
 * This maintains backward compatibility while specific events provide more detail.
 */
class DeploymentChanged extends BaseK8sResourceEvent
{
    public function __construct(
        string $namespace,
        string $clusterName,
        int $clusterId,
        string $resourceName,
        array $resource,
        string $action
    ) {
        parent::__construct(
            $namespace,
            $clusterName,
            $clusterId,
            'deployment',
            $resourceName,
            $resource,
            $action
        );
    }

    public function broadcastAs(): string
    {
        return 'deployment.changed';
    }
}
