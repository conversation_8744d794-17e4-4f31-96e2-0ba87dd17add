<?php

namespace App\Events\K8s;

/**
 * Generic event for any secret changes.
 * This maintains backward compatibility while specific events provide more detail.
 */
class SecretChanged extends BaseK8sResourceEvent
{
    public function __construct(
        string $namespace,
        string $clusterName,
        int $clusterId,
        string $resourceName,
        array $resource,
        string $action
    ) {
        parent::__construct(
            $namespace,
            $clusterName,
            $clusterId,
            'secret',
            $resourceName,
            $resource,
            $action
        );
    }

    public function broadcastAs(): string
    {
        return 'secret.changed';
    }
}
