<?php

namespace App\Events\K8s;

/**
 * Generic event for any ingress changes.
 * This maintains backward compatibility while specific events provide more detail.
 */
class IngressChanged extends BaseK8sResourceEvent
{
    public function __construct(
        string $namespace,
        string $clusterName,
        int $clusterId,
        string $resourceName,
        array $resource,
        string $action
    ) {
        parent::__construct(
            $namespace,
            $clusterName,
            $clusterId,
            'ingress',
            $resourceName,
            $resource,
            $action
        );
    }

    public function broadcastAs(): string
    {
        return 'ingress.changed';
    }
}
