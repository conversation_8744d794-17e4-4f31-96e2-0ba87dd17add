<?php

namespace App\Events\K8s;

/**
 * Generic event for any statefulset changes.
 * This maintains backward compatibility while specific events provide more detail.
 */
class StatefulSetChanged extends BaseK8sResourceEvent
{
    public function __construct(
        string $namespace,
        string $clusterName,
        int $clusterId,
        string $resourceName,
        array $resource,
        string $action
    ) {
        parent::__construct(
            $namespace,
            $clusterName,
            $clusterId,
            'statefulset',
            $resourceName,
            $resource,
            $action
        );
    }

    public function broadcastAs(): string
    {
        return 'statefulset.changed';
    }
}
