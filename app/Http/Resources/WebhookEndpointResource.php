<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WebhookEndpointResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'url' => $this->url,
            'is_active' => $this->is_active,
            'events' => $this->events,
            'headers' => $this->headers,
            'timeout' => $this->timeout,
            'max_attempts' => $this->max_attempts,
            'last_delivered_at' => $this->last_delivered_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'deliveries_count' => $this->whenCounted('deliveries'),
            'secret' => $this->when($request->routeIs('webhooks.show', 'webhooks.edit'), $this->decrypted_secret),
        ];
    }
}
