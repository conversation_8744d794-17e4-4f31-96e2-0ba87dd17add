export interface WebhookEndpoint {
  id: number;
  name: string;
  url: string;
  events: string[];
  headers: Record<string, string> | null;
  is_active: boolean;
  secret: string | null;
  timeout: number;
  max_attempts: number;
  last_delivered_at: string | null;
  created_at: string;
  updated_at: string;
  deliveries_count?: number;
  success_rate?: number;
}

export interface WebhookDelivery {
  id: number;
  webhook_endpoint_id: number;
  event_type: string;
  payload: Record<string, any>;
  status: 'pending' | 'success' | 'failed';
  attempt: number;
  http_status_code: number | null;
  response_body: string | null;
  error_message: string | null;
  duration: number | null;
  delivered_at: string | null;
  created_at: string;
  updated_at: string;
  formatted_duration?: string;
}

export interface WebhookStats {
  total: number;
  successful: number;
  failed: number;
  pending: number;
  success_rate: number;
}

export interface WebhookFormData {
  name: string;
  url: string;
  events: string[];
  headers: Record<string, string>;
  is_active: boolean;
  timeout?: number;
  max_attempts?: number;
  [key: string]: any; // 添加索引签名以满足 FormDataType 约束
}

export interface WebhookTestResult {
  success: boolean;
  message?: string;
  error_message?: string;
  delivery_id?: number;
  status?: string;
  http_status_code?: number;
  duration?: string;
}
