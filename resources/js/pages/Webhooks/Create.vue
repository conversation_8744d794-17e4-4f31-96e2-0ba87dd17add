<script setup lang="ts">
import { Head, Link, router, useForm } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import AppLayout from '@/layouts/AppLayout.vue';
import { ArrowLeft, Plus, Trash2, Info } from 'lucide-vue-next';
import { ref } from 'vue';
import type { WebhookFormData } from '@/types/webhook';
import type { BreadcrumbItemType } from '@/types';

const breadcrumbs: BreadcrumbItemType[] = [
  {
    title: 'Webhook 管理',
    href: '/webhooks',
  },
  {
    title: '创建 Webhook',
    href: '/webhooks/create',
  },
];

// 可用的事件类型
const availableEvents = [
  { value: '*', label: '全部事件', description: '接收所有平台事件（性能影响较大）' },
  { value: 'deployment.created', label: 'Deployment 创建', description: '当创建新的 Deployment 时触发' },
  { value: 'deployment.updated', label: 'Deployment 更新', description: '当 Deployment 配置更新时触发' },
  { value: 'deployment.deleted', label: 'Deployment 删除', description: '当删除 Deployment 时触发' },
  { value: 'deployment.scaled', label: 'Deployment 扩缩容', description: '当 Deployment 副本数变化时触发' },
  { value: 'statefulset.created', label: 'StatefulSet 创建', description: '当创建新的 StatefulSet 时触发' },
  { value: 'statefulset.updated', label: 'StatefulSet 更新', description: '当 StatefulSet 配置更新时触发' },
  { value: 'statefulset.deleted', label: 'StatefulSet 删除', description: '当删除 StatefulSet 时触发' },
  { value: 'service.created', label: 'Service 创建', description: '当创建新的 Service 时触发' },
  { value: 'service.updated', label: 'Service 更新', description: '当 Service 配置更新时触发' },
  { value: 'service.deleted', label: 'Service 删除', description: '当删除 Service 时触发' },
  { value: 'pod.created', label: 'Pod 创建', description: '当创建新的 Pod 时触发' },
  { value: 'pod.updated', label: 'Pod 状态变化', description: '当 Pod 状态发生变化时触发' },
  { value: 'pod.deleted', label: 'Pod 删除', description: '当删除 Pod 时触发' },
];

const form = useForm<WebhookFormData>({
  name: '',
  url: '',
  events: [],
  headers: {},
  is_active: true,
});

const newHeaderKey = ref('');
const newHeaderValue = ref('');

const addHeader = () => {
  if (newHeaderKey.value && newHeaderValue.value) {
    form.headers[newHeaderKey.value] = newHeaderValue.value;
    newHeaderKey.value = '';
    newHeaderValue.value = '';
  }
};

const removeHeader = (key: string) => {
  delete form.headers[key];
};

const toggleEvent = (eventValue: string) => {
  const index = form.events.indexOf(eventValue);
  if (index > -1) {
    form.events.splice(index, 1);
  } else {
    // 如果选择了全部事件，清除其他选项
    if (eventValue === '*') {
      form.events = ['*'];
    } else {
      // 如果选择了具体事件，移除全部事件选项
      const allIndex = form.events.indexOf('*');
      if (allIndex > -1) {
        form.events.splice(allIndex, 1);
      }
      form.events.push(eventValue);
    }
  }
};

const submit = () => {
  form.post(route('webhooks.store'), {
    onSuccess: () => {
      toast.success('创建成功', {
        description: '已成功创建 Webhook 端点'
      });
    },
    onError: (errors) => {
      toast.error('创建失败', {
        description: Object.values(errors)[0] as string
      });
    }
  });
};
</script>

<template>
  <Head title="创建 Webhook" />
  
  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="p-4 space-y-6">
    <!-- 头部 -->
    <div class="flex items-center gap-4">
      <Button variant="ghost" size="sm" as-child>
        <Link :href="route('webhooks.index')">
          <ArrowLeft class="h-4 w-4" />
        </Link>
      </Button>
      
      <div>
        <h1 class="text-3xl font-bold">创建 Webhook</h1>
        <p class="text-muted-foreground mt-2">
          配置新的 Webhook 端点来接收平台事件通知
        </p>
      </div>
    </div>

    <form @submit.prevent="submit" class="space-y-6">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 基础配置 -->
        <div class="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>基础信息</CardTitle>
              <CardDescription>
                配置 Webhook 的基本信息和目标 URL
              </CardDescription>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="space-y-2">
                <Label for="name">名称</Label>
                <Input
                  id="name"
                  v-model="form.name"
                  placeholder="例如：生产环境通知"
                />
                <p v-if="form.errors && 'name' in form.errors" class="text-sm text-destructive">
                  {{ (form.errors as any).name }}
                </p>
              </div>

              <div class="space-y-2">
                <Label for="url">Webhook URL</Label>
                <Input
                  id="url"
                  v-model="form.url"
                  type="url"
                  placeholder="https://your-app.com/webhooks/endpoint"
                />
                <p v-if="form.errors && 'url' in form.errors" class="text-sm text-destructive">
                  {{ (form.errors as any).url }}
                </p>
                <p class="text-sm text-muted-foreground">
                  当事件发生时，我们将向此 URL 发送 HTTP POST 请求
                </p>
              </div>

              <div class="flex items-center space-x-2">
                <Switch
                  id="is_active"
                  v-model:checked="form.is_active"
                />
                <Label for="is_active">启用此 Webhook</Label>
              </div>
            </CardContent>
          </Card>

          <!-- 事件订阅 -->
          <Card>
            <CardHeader>
              <CardTitle>事件订阅</CardTitle>
              <CardDescription>
                选择要接收的事件类型。选择"全部事件"会影响性能，建议只订阅需要的事件。
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div class="mb-4 p-4 rounded-lg bg-blue-50 dark:bg-blue-950/50 border border-blue-200 dark:border-blue-800">
                <div class="flex items-start gap-2">
                  <Info class="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                  <p class="text-sm text-blue-800 dark:text-blue-200">
                    订阅"全部事件"会接收大量通知，可能影响性能。建议根据实际需要选择具体事件类型。
                  </p>
                </div>
              </div>

              <div class="space-y-3">
                <div
                  v-for="event in availableEvents"
                  :key="event.value"
                  class="flex items-start space-x-3 p-3 rounded-lg border hover:bg-muted/50 transition-colors"
                  :class="{ 'bg-muted': form.events.includes(event.value) }"
                >
                  <Checkbox
                    :checked="form.events.includes(event.value)"
                    @update:checked="toggleEvent(event.value)"
                    class="mt-1"
                  />
                  <div class="flex-1 space-y-1">
                    <div class="flex items-center gap-2">
                      <Label class="font-medium cursor-pointer">
                        {{ event.label }}
                      </Label>
                      <Badge
                        v-if="event.value === '*'"
                        variant="secondary"
                        class="text-xs"
                      >
                        性能影响
                      </Badge>
                    </div>
                    <p class="text-sm text-muted-foreground">
                      {{ event.description }}
                    </p>
                  </div>
                </div>
              </div>

              <p v-if="form.errors && 'events' in form.errors" class="text-sm text-destructive mt-2">
                {{ (form.errors as any).events }}
              </p>

              <div v-if="form.events.length > 0" class="mt-4 pt-4 border-t">
                <Label class="text-sm font-medium">已选择事件：</Label>
                <div class="flex flex-wrap gap-2 mt-2">
                  <Badge
                    v-for="event in form.events"
                    :key="event"
                    variant="outline"
                  >
                    {{ availableEvents.find(e => e.value === event)?.label || event }}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 自定义请求头 -->
          <Card>
            <CardHeader>
              <CardTitle>自定义请求头</CardTitle>
              <CardDescription>
                为 Webhook 请求添加自定义 HTTP 头部
              </CardDescription>
            </CardHeader>
            <CardContent class="space-y-4">
              <!-- 添加新头部 -->
              <div class="flex gap-2">
                <Input
                  v-model="newHeaderKey"
                  placeholder="Header 名称"
                  class="flex-1"
                />
                <Input
                  v-model="newHeaderValue"
                  placeholder="Header 值"
                  class="flex-1"
                />
                <Button
                  type="button"
                  size="sm"
                  @click="addHeader"
                  :disabled="!newHeaderKey || !newHeaderValue"
                >
                  <Plus class="h-4 w-4" />
                </Button>
              </div>

              <!-- 已添加的头部列表 -->
              <div v-if="Object.keys(form.headers).length > 0" class="space-y-2">
                <Separator />
                <div
                  v-for="(value, key) in form.headers"
                  :key="key"
                  class="flex items-center justify-between p-2 rounded border"
                >
                  <div class="flex-1">
                    <span class="font-mono text-sm font-medium">{{ key }}:</span>
                    <span class="font-mono text-sm ml-2">{{ value }}</span>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    @click="removeHeader(key)"
                  >
                    <Trash2 class="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <p v-if="form.errors && 'headers' in form.errors" class="text-sm text-destructive">
                {{ (form.errors as any).headers }}
              </p>
            </CardContent>
          </Card>
        </div>

        <!-- 侧边栏信息 -->
        <div class="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Webhook 说明</CardTitle>
            </CardHeader>
            <CardContent class="space-y-4 text-sm">
              <div>
                <h4 class="font-medium mb-2">安全签名</h4>
                <p class="text-muted-foreground">
                  每个 Webhook 请求都会包含签名头部，用于验证请求来源的真实性。
                </p>
              </div>

              <Separator />

              <div>
                <h4 class="font-medium mb-2">重试机制</h4>
                <p class="text-muted-foreground">
                  如果请求失败，系统会自动重试，重试次数和超时时间由系统配置决定。
                </p>
              </div>

              <Separator />

              <div>
                <h4 class="font-medium mb-2">事件格式</h4>
                <p class="text-muted-foreground">
                  所有事件都会以 JSON 格式发送，包含事件类型、时间戳和相关数据。
                </p>
              </div>
            </CardContent>
          </Card>

          <!-- 操作按钮 -->
          <div class="space-y-3">
            <Button
              type="submit"
              class="w-full"
              :disabled="form.processing"
            >
              {{ form.processing ? '创建中...' : '创建 Webhook' }}
            </Button>

            <Button
              variant="outline"
              class="w-full"
              as-child
            >
              <Link :href="route('webhooks.index')">
                取消
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </form>
    </div>
  </AppLayout>
</template>
